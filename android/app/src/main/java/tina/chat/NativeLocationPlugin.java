package tina.chat;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import android.location.Location;
import android.location.LocationListener;
import android.location.LocationManager;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;

import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.getcapacitor.JSObject;
import com.getcapacitor.Plugin;
import com.getcapacitor.PluginCall;
import com.getcapacitor.PluginMethod;
import com.getcapacitor.annotation.CapacitorPlugin;
import com.getcapacitor.annotation.Permission;

@CapacitorPlugin(
    name = "NativeLocation",
    permissions = {
        @Permission(strings = {Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.ACCESS_COARSE_LOCATION})
    }
)
public class NativeLocationPlugin extends Plugin {
    
    private LocationManager locationManager;
    private static final int LOCATION_PERMISSION_REQUEST = 1001;
    
    @Override
    public void load() {
        locationManager = (LocationManager) getContext().getSystemService(Context.LOCATION_SERVICE);
    }
    
    @PluginMethod
    public void checkPermissions(PluginCall call) {
        JSObject result = new JSObject();
        
        boolean fineLocationGranted = ContextCompat.checkSelfPermission(
            getContext(), 
            Manifest.permission.ACCESS_FINE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED;
        
        boolean coarseLocationGranted = ContextCompat.checkSelfPermission(
            getContext(), 
            Manifest.permission.ACCESS_COARSE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED;
        
        boolean granted = fineLocationGranted || coarseLocationGranted;
        String status = granted ? "granted" : "denied";
        
        result.put("granted", granted);
        result.put("status", status);
        call.resolve(result);
    }
    
    @PluginMethod
    public void requestPermissions(PluginCall call) {
        if (ContextCompat.checkSelfPermission(getContext(), Manifest.permission.ACCESS_FINE_LOCATION) 
            == PackageManager.PERMISSION_GRANTED ||
            ContextCompat.checkSelfPermission(getContext(), Manifest.permission.ACCESS_COARSE_LOCATION) 
            == PackageManager.PERMISSION_GRANTED) {
            
            JSObject result = new JSObject();
            result.put("granted", true);
            result.put("status", "granted");
            call.resolve(result);
            return;
        }
        
        // 保存 call 以便在权限回调中使用
        saveCall(call);
        
        // 请求权限
        ActivityCompat.requestPermissions(
            getActivity(),
            new String[]{
                Manifest.permission.ACCESS_FINE_LOCATION,
                Manifest.permission.ACCESS_COARSE_LOCATION
            },
            LOCATION_PERMISSION_REQUEST
        );
    }
    
    @PluginMethod
    public void getCurrentPosition(PluginCall call) {
        if (!hasLocationPermission()) {
            call.reject("Location permission not granted");
            return;
        }
        
        if (!checkLocationEnabled()) {
            call.reject("Location services are disabled");
            return;
        }
        
        // 获取配置参数
        boolean enableHighAccuracy = call.getBoolean("enableHighAccuracy", true);
        int timeout = call.getInt("timeout", 10000);
        
        // 选择位置提供者
        String provider = enableHighAccuracy ? LocationManager.GPS_PROVIDER : LocationManager.NETWORK_PROVIDER;
        
        // 如果首选提供者不可用，尝试其他提供者
        if (!locationManager.isProviderEnabled(provider)) {
            if (provider.equals(LocationManager.GPS_PROVIDER) && 
                locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER)) {
                provider = LocationManager.NETWORK_PROVIDER;
            } else if (provider.equals(LocationManager.NETWORK_PROVIDER) && 
                       locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER)) {
                provider = LocationManager.GPS_PROVIDER;
            } else {
                call.reject("No location provider available");
                return;
            }
        }
        
        // 创建位置监听器
        LocationListener locationListener = new LocationListener() {
            @Override
            public void onLocationChanged(Location location) {
                locationManager.removeUpdates(this);
                
                JSObject result = new JSObject();
                result.put("latitude", location.getLatitude());
                result.put("longitude", location.getLongitude());
                result.put("accuracy", location.getAccuracy());
                result.put("timestamp", location.getTime());
                
                call.resolve(result);
            }
            
            @Override
            public void onStatusChanged(String provider, int status, Bundle extras) {}
            
            @Override
            public void onProviderEnabled(String provider) {}
            
            @Override
            public void onProviderDisabled(String provider) {
                locationManager.removeUpdates(this);
                call.reject("Location provider disabled");
            }
        };
        
        try {
            // 请求位置更新
            locationManager.requestLocationUpdates(
                provider,
                0,
                0,
                locationListener
            );
            
            // 设置超时
            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                locationManager.removeUpdates(locationListener);
                call.reject("Location request timeout");
            }, timeout);
            
        } catch (SecurityException e) {
            call.reject("Location permission not granted");
        }
    }
    
    @PluginMethod
    public void isLocationEnabled(PluginCall call) {
        JSObject result = new JSObject();
        boolean enabled = checkLocationEnabled();
        result.put("enabled", enabled);
        call.resolve(result);
    }

    private boolean checkLocationEnabled() {
        return locationManager != null &&
               (locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER) ||
                locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER));
    }

    @PluginMethod
    public void testPlugin(PluginCall call) {
        JSObject result = new JSObject();
        result.put("message", "NativeLocation plugin is working!");
        result.put("timestamp", System.currentTimeMillis());
        call.resolve(result);
    }

    private boolean hasLocationPermission() {
        return ContextCompat.checkSelfPermission(getContext(), Manifest.permission.ACCESS_FINE_LOCATION) 
               == PackageManager.PERMISSION_GRANTED ||
               ContextCompat.checkSelfPermission(getContext(), Manifest.permission.ACCESS_COARSE_LOCATION) 
               == PackageManager.PERMISSION_GRANTED;
    }
    
    @Override
    protected void handleRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.handleRequestPermissionsResult(requestCode, permissions, grantResults);
        
        if (requestCode == LOCATION_PERMISSION_REQUEST) {
            PluginCall savedCall = getSavedCall();
            if (savedCall == null) {
                return;
            }
            
            boolean granted = false;
            for (int result : grantResults) {
                if (result == PackageManager.PERMISSION_GRANTED) {
                    granted = true;
                    break;
                }
            }
            
            JSObject result = new JSObject();
            result.put("granted", granted);
            result.put("status", granted ? "granted" : "denied");
            savedCall.resolve(result);
        }
    }
}
