package tina.chat;

import android.os.Bundle;
import android.view.View;
import android.webkit.JavascriptInterface;

import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import com.getcapacitor.BridgeActivity;
import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;

import java.util.List;

public class MainActivity extends BridgeActivity {
    
    private FunASRManager funASRManager;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 注册原生定位插件
        registerPlugin(NativeLocationPlugin.class);

        // 注入权限管理到 WebView
        addPermissionInterface();
        
        // 注入FunASR接口到 WebView
        addFunASRInterface();
        
        // get root view
        View rootView = getWindow().getDecorView().getRootView();
        ViewCompat.setOnApplyWindowInsetsListener(rootView, (v, insets) -> {
            var systemBars = WindowInsetsCompat.Type.systemBars();
            var ime = WindowInsetsCompat.Type.ime();

            // Get insets for both system bars and keyboard
            Insets allInsets = insets.getInsets(WindowInsetsCompat.Type.systemBars() | WindowInsetsCompat.Type.ime());

            // Or if you want different handling:
            Insets systemBarInsets = insets.getInsets(systemBars);
            Insets imeInsets = insets.getInsets(ime);

            // Only apply keyboard insets to bottom padding
            v.setPadding(
                    systemBarInsets.left,
                    systemBarInsets.top,
                    systemBarInsets.right,
                    systemBarInsets.bottom + (Math.max(imeInsets.bottom, 0))
            );
            return insets;
        });
    }
    
    @Override
    public void onDestroy() {
        super.onDestroy();
        // 清理FunASR资源
        if (funASRManager != null) {
            funASRManager.cleanup();
        }
    }
    
    private void addPermissionInterface() {
        // 等待 WebView 初始化完成后注入接口
        getBridge().getWebView().addJavascriptInterface(new PermissionInterface(), "AndroidPermissions");
    }
    
    private void addFunASRInterface() {
        // 创建FunASR管理器并注入接口
        funASRManager = new FunASRManager(this, getBridge().getWebView());
        getBridge().getWebView().addJavascriptInterface(funASRManager, "AndroidFunASR");
    }

    public class PermissionInterface {
        
        @JavascriptInterface
        public void checkMicrophonePermission(String callbackName) {
            runOnUiThread(() -> {
                boolean hasPermission = XXPermissions.isGranted(MainActivity.this, Permission.RECORD_AUDIO);
                
                String result = String.format(
                    "{ \"hasPermission\": %s, \"permission\": \"microphone\" }",
                    hasPermission
                );
                
                executeCallback(callbackName, result);
            });
        }
        
        @JavascriptInterface
        public void requestMicrophonePermission(String callbackName) {
            runOnUiThread(() -> {
                XXPermissions.with(MainActivity.this)
                    .permission(Permission.RECORD_AUDIO)
                    .request(new OnPermissionCallback() {
                        @Override
                        public void onGranted(List<String> permissions, boolean allGranted) {
                            String result = String.format(
                                "{ \"granted\": true, \"permission\": \"microphone\", \"message\": \"麦克风权限已授权\" }",
                                allGranted
                            );
                            executeCallback(callbackName, result);
                        }

                        @Override
                        public void onDenied(List<String> permissions, boolean doNotAskAgain) {
                            String message = doNotAskAgain ? 
                                "麦克风权限已被永久拒绝，请前往设置页面手动开启" : 
                                "麦克风权限被拒绝";
                            
                            String result = String.format(
                                "{ \"granted\": false, \"permission\": \"microphone\", \"doNotAskAgain\": %s, \"message\": \"%s\" }",
                                doNotAskAgain, message
                            );
                            executeCallback(callbackName, result);
                        }
                    });
            });
        }
        
        @JavascriptInterface
        public void openAppSettings(String callbackName) {
            runOnUiThread(() -> {
                XXPermissions.startPermissionActivity(MainActivity.this);
                
                String result = "{ \"success\": true, \"message\": \"已跳转到应用设置页面\" }";
                executeCallback(callbackName, result);
            });
        }
    }
    
    private void executeCallback(String callbackName, String result) {
        String script = String.format("if(window.%s) { window.%s(%s); }", 
            callbackName, callbackName, result);
        
        getBridge().getWebView().post(() -> {
            getBridge().getWebView().evaluateJavascript(script, null);
        });
    }
}
