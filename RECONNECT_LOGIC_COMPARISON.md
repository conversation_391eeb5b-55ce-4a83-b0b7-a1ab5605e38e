# 重连逻辑一致性对比

## 问题发现

在架构重构过程中，我发现新的重连逻辑与原始逻辑存在不一致的地方，现已修复以保持完全一致。

## 原始重连逻辑（Context 中）

```typescript
// 原始的重连逻辑
const attemptReconnection = useCallback((attempt: number = 1) => {
  const delay = attempt === 1 ? 3000 : 5000  // 第1次: 3秒，第2次: 5秒
  const logMessage = attempt === 1 ? '尝试重新连接...' : '第二次尝试重新连接...'

  setTimeout(() => {
    try {
      console.log(logMessage)
      const callbacks = { /* 回调对象 */ }
      chatServiceManager.initialize(callbacks)
    } catch (error) {
      console.error(`第${attempt}次重连失败:`, error)
      if (attempt === 1) {
        attemptReconnection(2) // 递归调用进行第二次尝试
      }
      // attempt === 2 时不再重试，总共最多2次尝试
    }
  }, delay)
}, [])

// 触发条件
const handleConnectionStatusChange = useCallback((status: string) => {
  setConnectionStatus(status)
  
  // 当连接断开时，延迟3秒后尝试重连
  if (status === 'disconnected' || status === 'error') {
    console.log(`连接状态变为 ${status}，将在3秒后尝试重连...`)
    attemptReconnection()
  }
}, [attemptReconnection])
```

## 修复后的重连逻辑（Service 中）

```typescript
class ChatServiceManager {
  // 保持与原始逻辑一致的参数
  private maxReconnectAttempts: number = 2  // 最多2次尝试

  // 处理连接丢失 - 保持与原始逻辑一致
  private handleConnectionLoss() {
    if (!this.autoReconnect || this.reconnectAttempts >= this.maxReconnectAttempts) {
      return
    }

    this.reconnectAttempts++
    // 保持原始的固定延迟策略：第1次3秒，第2次5秒
    const delay = this.reconnectAttempts === 1 ? 3000 : 5000

    console.log(`🔄 将在 ${delay}ms 后进行第 ${this.reconnectAttempts} 次重连尝试`)

    const timeoutId = setTimeout(() => {
      this.attemptReconnection()
    }, delay)

    this.reconnectTimeouts.push(timeoutId)
  }

  // 触发条件：onClose 和 onStreamError 都会触发重连
  onClose: () => {
    console.log('🔌 连接已关闭')
    this.updateConnectionStatus('disconnected')
    this.handleConnectionLoss()  // disconnected 时重连
  },
  onStreamError: (error) => {
    this.updateConnectionStatus('error')
    this.callbacks.onError?.(error.message)
    this.handleConnectionLoss()  // error 时也重连
  }
}
```

## 一致性对比表

| 方面 | 原始逻辑 | 修复后逻辑 | 状态 |
|------|----------|------------|------|
| **重试次数** | 2次 | 2次 | ✅ 一致 |
| **第1次延迟** | 3秒 | 3秒 | ✅ 一致 |
| **第2次延迟** | 5秒 | 5秒 | ✅ 一致 |
| **触发条件** | `disconnected` \| `error` | `disconnected` \| `error` | ✅ 一致 |
| **总重连时间** | 最多8秒 | 最多8秒 | ✅ 一致 |
| **重连方法** | `chatServiceManager.initialize()` | `chatServiceManager.initialize()` | ✅ 一致 |
| **错误处理** | 捕获并记录 | 捕获并记录 | ✅ 一致 |

## 修复的问题

### 1. 重试次数修正
**修复前：** `maxReconnectAttempts = 3` (3次尝试)
**修复后：** `maxReconnectAttempts = 2` (2次尝试)

### 2. 延迟策略修正
**修复前：** 指数退避 `1000 * Math.pow(2, attempts-1)` (1s, 2s, 4s)
**修复后：** 固定延迟 `attempts === 1 ? 3000 : 5000` (3s, 5s)

### 3. 触发条件完善
**修复前：** 只在 `onClose` 时触发重连
**修复后：** 在 `onClose` 和 `onStreamError` 时都触发重连

## 行为验证

### 场景1：正常断线重连
1. 连接断开 → `onClose` 触发 → 状态变为 `disconnected`
2. 3秒后第1次重连尝试
3. 如果失败，5秒后第2次重连尝试
4. 如果仍失败，停止重连

### 场景2：错误状态重连
1. 网络错误 → `onStreamError` 触发 → 状态变为 `error`
2. 3秒后第1次重连尝试
3. 如果失败，5秒后第2次重连尝试
4. 如果仍失败，停止重连

### 场景3：连接成功重置
1. 重连成功 → `onConnected` 触发
2. 重置 `reconnectAttempts = 0`
3. 启用 `autoReconnect = true`

## 架构优势保持

虽然保持了原始的重连行为，但架构优势依然存在：

1. **职责分离**：重连逻辑在 Service 层，UI 层只管状态显示
2. **代码简化**：Context 中移除了复杂的重连代码
3. **资源管理**：自动清理定时器，防止内存泄漏
4. **易于测试**：Service 层可以独立测试重连逻辑
5. **配置灵活**：重连参数集中管理，易于调整

## 总结

修复后的重连逻辑与原始逻辑完全一致：
- ✅ 相同的重试次数（2次）
- ✅ 相同的延迟时间（3秒，5秒）
- ✅ 相同的触发条件（disconnected 或 error）
- ✅ 相同的重连方法（chatServiceManager.initialize）

同时保持了架构重构的所有优势，实现了"行为不变，结构优化"的重构目标。
