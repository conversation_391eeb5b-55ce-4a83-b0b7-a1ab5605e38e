import React, { useEffect, useState } from 'react'
import { betaInfo } from '../data/betaInfo'

interface BetaInfoDialogProps {
  isOpen: boolean
  onClose: () => void
}

const BetaInfoDialog: React.FC<BetaInfoDialogProps> = ({ isOpen, onClose }) => {
  const [countdown, setCountdown] = useState(50)

  useEffect(() => {
    if (!isOpen) return

    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          onClose()
          return 50
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(timer)
  }, [isOpen, onClose])

  if (!isOpen) return null

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose()
    }
  }

  return (
    <div
      className='fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4'
      onClick={handleBackdropClick}
    >
      <div className='animate-in fade-in-0 zoom-in-95 mx-4 w-full max-w-sm overflow-hidden rounded-2xl bg-white shadow-xl duration-200'>
        {/* 标题 */}
        <div className='border-b border-gray-100 px-6 pb-4 pt-6'>
          <h3 className='text-center text-lg font-semibold text-gray-900'>
            Tina.Chat 内测说明
          </h3>
        </div>

        {/* 内容 */}
        <div className='max-h-96 overflow-y-auto px-6 py-4'>
          {/* 版本信息 */}
          <div className='mb-6'>
            <h4 className='mb-2 flex items-center text-sm font-medium text-gray-900'>
              <span className='mr-2 h-2 w-2 rounded-full bg-blue-500'></span>
              版本信息
            </h4>
            <p className='pl-4 text-sm text-gray-600'>{betaInfo.versionInfo}</p>
          </div>

          {/* 更新说明 */}
          <div className='mb-4'>
            <h4 className='mb-2 flex items-center text-sm font-medium text-gray-900'>
              <span className='mr-2 h-2 w-2 rounded-full bg-green-500'></span>
              更新说明
            </h4>
            <ul className='space-y-1 pl-4'>
              {betaInfo.updateNotes.map((note, index) => (
                <li
                  key={index}
                  className='flex items-start text-sm text-gray-600'
                >
                  <span className='mr-2 mt-1 text-xs text-green-500'>•</span>
                  {note}
                </li>
              ))}
            </ul>
          </div>
        </div>
        {/* 已知问题 */}
        <div className='max-h-96 overflow-y-auto px-6 py-4'>
          <div className='mb-6'>
            <h4 className='mb-2 flex items-center text-sm font-medium text-gray-900'>
              <span className='mr-2 h-2 w-2 rounded-full bg-orange-500'></span>
              已知问题
            </h4>
            <ul className='space-y-1 pl-4'>
              {betaInfo.knownIssues.map((issue, index) => (
                <li
                  key={index}
                  className='flex items-start text-sm text-gray-600'
                >
                  <span className='mr-2 mt-1 text-xs text-orange-500'>•</span>
                  {issue}
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* 底部按钮 */}
        <div className='border-t border-gray-100'>
          <button
            onClick={onClose}
            className='flex w-full items-center justify-center px-6 py-4 text-base font-medium text-blue-600 transition-colors hover:bg-gray-50'
          >
            关闭({countdown}s)
          </button>
        </div>
      </div>
    </div>
  )
}

export default BetaInfoDialog
