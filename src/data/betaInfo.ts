export interface BetaInfo {
  versionInfo: string
  knownIssues: string[]
  updateNotes: string[]
}

export const betaInfo: BetaInfo = {
  versionInfo: 'v1.0.0 alpha 0710',
  knownIssues: [
    "因为浏览器 SSE 流式连接数的限制，超过 6 个回话，可能会出现连接等待的情况，表现为不响应",
    "默认会显示一些测试消息，请忽略",
    "过长消息时，摘要处理要更完善",
    "复杂任务显示未完成",
    "有些图片没有做验证，有时会出现打不开的情况",
  ],
  updateNotes: [
    '0710：新增语音消息',
    '0710：修改前端闲置过久，无法响应的 bug',
    '0710：修复有时不会出现 快速回复 的 bug',
    '0710：修改前端 UI样式',
    '0710：增加消息预处理，降低幻觉、美化输出样式',
    '0628：修复图片加载失败的问题',
    '0628：Tinatask 自动每天处理新闻，汇总信息',
    '0628：新增图片上传功能，支持对图片的修改，暂不支持描述',
    '0628：增强 tina task 制作网站、攻略、游戏的能力',
    '0619：增加历史记录加载功能【还未全部完成】',
    '0619：增加语音回复功能 （用你会说话吗，可以触发），目前是语音消息和文本消息分开。后面会给文本消息都加上语音播放',
    '      语音输出功能只完成基础功能，后续 UI 和 功能会优化',
    '0617：新增思维导图，在聊天过程中让小天绘制，或者在遇到复杂逻辑时，小天会自动绘制',
    '0617：修复连续发送消息时，不会中断前一次回答的 bug ',
    '0616：支持新闻卡片 , 点击跳转到预览页',
    '0616：支持 图片、视频 卡片',
    '0616：支持 支持图片生成',
    '0616：支持 TinaTask 工具',
  ],
}
