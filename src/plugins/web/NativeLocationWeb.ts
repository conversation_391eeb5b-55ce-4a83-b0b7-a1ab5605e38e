import { WebPlugin } from '@capacitor/core'
import type { NativeLocationPlugin } from '../NativeLocation'

export class NativeLocationWeb extends WebPlugin implements NativeLocationPlugin {
  async checkPermissions(): Promise<{ granted: boolean; status: string }> {
    if (!navigator.geolocation) {
      return { granted: false, status: 'not_supported' }
    }

    if ('permissions' in navigator) {
      try {
        const permission = await navigator.permissions.query({ name: 'geolocation' as PermissionName })
        return {
          granted: permission.state === 'granted',
          status: permission.state
        }
      } catch (error) {
        console.log('permissions API 不支持')
      }
    }

    return { granted: false, status: 'prompt' }
  }

  async requestPermissions(): Promise<{ granted: boolean; status: string }> {
    // Web 端通过实际调用 getCurrentPosition 来触发权限请求
    return new Promise((resolve) => {
      if (!navigator.geolocation) {
        resolve({ granted: false, status: 'not_supported' })
        return
      }

      navigator.geolocation.getCurrentPosition(
        () => resolve({ granted: true, status: 'granted' }),
        (error) => {
          if (error.code === error.PERMISSION_DENIED) {
            resolve({ granted: false, status: 'denied' })
          } else {
            resolve({ granted: false, status: 'error' })
          }
        },
        { timeout: 5000 }
      )
    })
  }

  async getCurrentPosition(options?: {
    enableHighAccuracy?: boolean
    timeout?: number
    maximumAge?: number
  }): Promise<{
    latitude: number
    longitude: number
    accuracy: number
    timestamp: number
  }> {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('Geolocation not supported'))
        return
      }

      navigator.geolocation.getCurrentPosition(
        (position) => {
          resolve({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
            timestamp: position.timestamp
          })
        },
        (error) => {
          reject(new Error(`Geolocation error: ${error.message}`))
        },
        {
          enableHighAccuracy: options?.enableHighAccuracy ?? true,
          timeout: options?.timeout ?? 10000,
          maximumAge: options?.maximumAge ?? 60000
        }
      )
    })
  }

  async isLocationEnabled(): Promise<{ enabled: boolean }> {
    return { enabled: !!navigator.geolocation }
  }
}
