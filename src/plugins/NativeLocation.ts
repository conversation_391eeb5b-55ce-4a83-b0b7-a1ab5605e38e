import { registerPlugin } from '@capacitor/core'

export interface NativeLocationPlugin {
  /**
   * 检查定位权限
   */
  checkPermissions(): Promise<{ granted: boolean; status: string }>
  
  /**
   * 请求定位权限
   */
  requestPermissions(): Promise<{ granted: boolean; status: string }>
  
  /**
   * 获取当前位置（使用 Android 原生 LocationManager）
   */
  getCurrentPosition(options?: {
    enableHighAccuracy?: boolean
    timeout?: number
    maximumAge?: number
  }): Promise<{
    latitude: number
    longitude: number
    accuracy: number
    timestamp: number
  }>
  
  /**
   * 检查位置服务是否启用
   */
  isLocationEnabled(): Promise<{ enabled: boolean }>
}

const NativeLocation = registerPlugin<NativeLocationPlugin>('NativeLocation', {
  web: () => import('./web/NativeLocationWeb').then(m => new m.NativeLocationWeb()),
})

export default NativeLocation
