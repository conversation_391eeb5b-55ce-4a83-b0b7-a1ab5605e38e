import React, { useCallback, useRef, useState, useEffect } from 'react'
import { useVoiceRecording } from '@/hooks/useVoiceRecording'
import VoiceWaveform from '../VoiceWaveform'
import { showToast } from '@/wechatComponents/Toast'
import { ensureMicrophonePermission, checkMicrophonePermission } from '@/utils/androidPermissions'

interface VoiceInputProps {
  onResult: (text: string) => void
  onError?: (error: string) => void
}

// 硬编码配置：切换语音输入模式
// 'click': 点击开始，再次点击结束
// 'hold': 按住开始，松开结束
const VOICE_INPUT_MODE: 'click' | 'hold' = 'hold'

// 录音时间限制配置
const RECORDING_WARNING_TIME = 31 // 31秒显示警告
const RECORDING_MAX_TIME = 150 // 39秒自动停止

const VoiceInput: React.FC<VoiceInputProps> = ({ onResult, onError }) => {
  const {
    isRecording,
    hasPermission,
    error,
    audioLevels,
    startRecording,
    stopRecording,
    cancelRecording,
    clearError,
    isSupported,
    setOnResult,
  } = useVoiceRecording()

  const isHoldingRef = useRef(false)
  const recordingTimerRef = useRef<NodeJS.Timeout | null>(null)
  const warningShownRef = useRef(false)
  const [recordingDuration, setRecordingDuration] = useState(0)
  const [isDraggedOut, setIsDraggedOut] = useState(false) // 是否拖拽出按钮区域
  const touchStartPositionRef = useRef<{ x: number; y: number } | null>(null) // 记录触摸开始位置
  const TOUCH_MOVE_THRESHOLD = 50 // 移动超过50px才认为是拖拽

  // 设置结果回调
  React.useEffect(() => {
    setOnResult(onResult)
  }, [onResult, setOnResult])

  // 开发环境下的调试信息（移除debug.ts相关代码）
  React.useEffect(() => {
    if (import.meta.env.DEV) {
      console.log('🎤 VoiceInput 组件初始化')
    }
  }, [])

  // 处理错误
  React.useEffect(() => {
    if (error) {
      // 使用 Toast 显示错误
      showToast({
        type: 'error',
        content: error
      })
      onError?.(error)
      // 自动清除错误
      setTimeout(clearError, 3000)
    }
  }, [error, onError, clearError])

  // 录音计时器
  useEffect(() => {
    if (isRecording) {
      // 重置计时器状态
      setRecordingDuration(0)
      warningShownRef.current = false
      
      // 启动计时器
      recordingTimerRef.current = setInterval(() => {
        setRecordingDuration(prev => {
          const newDuration = prev + 1
          
          // 31秒警告
          if (newDuration === RECORDING_WARNING_TIME && !warningShownRef.current) {
            warningShownRef.current = true
            showToast({
              type: 'error',
              content:  '最大录音时间不超过'+RECORDING_MAX_TIME+"秒" 
            })
          }
          
          // 39秒自动停止
          if (newDuration >= RECORDING_MAX_TIME) {
            stopRecording()
            return RECORDING_MAX_TIME
          }
          
          return newDuration
        })
      }, 1000)
    } else {
      // 停止录音时清理计时器
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current)
        recordingTimerRef.current = null
      }
      setRecordingDuration(0)
      warningShownRef.current = false
    }

    // 清理函数
    return () => {
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current)
        recordingTimerRef.current = null
      }
    }
  }, [isRecording, stopRecording])

  // 权限检查（只检查，不申请）
  const checkPermissionOnly = useCallback(async (): Promise<boolean> => {
    try {
      // 使用只检查权限的函数，不自动申请
      const result = await checkMicrophonePermission()
      return result.hasPermission || false
    } catch (error) {
      console.error('权限检查失败:', error)
      return false
    }
  }, [])

  // 权限检查和申请的统一处理函数（只在用户操作时调用）
  const checkAndRequestPermission = useCallback(async (): Promise<boolean> => {
    try {
      // 先检查权限
      const hasPermission = await checkPermissionOnly()
      if (hasPermission) {
        return true
      }

      // 没有权限时，申请权限
      const granted = await ensureMicrophonePermission(true)
      if (!granted) {
        // 权限被拒绝，显示提示信息
        showToast({
          type: 'error',
          content: '麦克风权限被拒绝，无法录音。请在设置中开启麦克风权限。'
        })
        onError?.('麦克风权限被拒绝，无法录音')
        return false
      }
      return true
    } catch (error) {
      console.error('权限检查失败:', error)
      showToast({
        type: 'error',
        content: '权限检查失败，请重试'
      })
      onError?.('权限检查失败')
      return false
    }
  }, [onError, checkPermissionOnly])

  // 点击模式：点击处理录音开始/停止
  const handleClick = useCallback(async (e: React.MouseEvent) => {
    e.stopPropagation()
    
    if (!isSupported()) {
      onError?.('浏览器不支持语音录制')
      return
    }
    
    // 如果是按住模式，点击事件不处理录音逻辑
    if (VOICE_INPUT_MODE === 'hold') {
      return
    }
    
    if (isRecording) {
      // 当前正在录音，停止录音
      await stopRecording()
    } else {
      // 当前未录音，先检查权限再开始录音
      const hasPermission = await checkAndRequestPermission()
      if (hasPermission) {
        await startRecording()
      }
      // 如果权限被拒绝，checkAndRequestPermission 已经处理了错误提示
    }
  }, [isRecording, startRecording, stopRecording, isSupported, checkAndRequestPermission])

  // 按住模式：鼠标按下开始录音
  const handleMouseDown = useCallback(async (e: React.MouseEvent) => {
    e.stopPropagation()
    console.log('🔧 MouseDown 事件触发', { 
      mode: VOICE_INPUT_MODE, 
      isRecording, 
      error, 
      isHolding: isHoldingRef.current 
    })
    
    if (VOICE_INPUT_MODE !== 'hold') {
      return
    }
    
    if (!isSupported()) {
      onError?.('浏览器不支持语音录制')
      return
    }
    
    if (error || isRecording) {
      console.log('🔧 MouseDown 早期返回', { error, isRecording })
      return
    }
    
    // 在移动端，优先使用触摸事件
    if ('ontouchstart' in window) {
      console.log('🔧 移动端设备，忽略鼠标按下事件')
      return
    }
    
    // 先检查权限再开始录音
    const hasPermission = await checkAndRequestPermission()
    if (!hasPermission) {
      // 权限被拒绝，取消本次操作
      return
    }
    
    isHoldingRef.current = true
    setIsDraggedOut(false) // 重置拖拽状态
    console.log('🔧 鼠标按下设置 isHolding=true, isDraggedOut=false')
    
    const success = await startRecording()
    console.log('🔧 MouseDown startRecording 结果', { success })
  }, [startRecording, isSupported, checkAndRequestPermission, error, isRecording])

  // 按住模式：鼠标松开结束录音
  const handleMouseUp = useCallback(async (e: React.MouseEvent) => {
    e.stopPropagation()
    console.log('🔧 MouseUp 事件触发', { 
      mode: VOICE_INPUT_MODE, 
      isHolding: isHoldingRef.current, 
      isRecording, 
      isDraggedOut 
    })
    
    if (VOICE_INPUT_MODE !== 'hold') {
      return
    }
    
    if (!isHoldingRef.current || !isRecording) {
      console.log('🔧 MouseUp 早期返回', { isHolding: isHoldingRef.current, isRecording })
      return
    }
    
    // 在移动端，优先使用触摸事件
    if ('ontouchstart' in window) {
      console.log('🔧 移动端设备，忽略鼠标抬起事件')
      return
    }
    
    isHoldingRef.current = false
    console.log('🔧 鼠标抬起设置 isHolding=false')
    
    // 如果已经拖拽出了按钮区域，说明已经取消了，不需要再处理
    if (isDraggedOut) {
      console.log('🔧 鼠标抬起：已经拖拽出区域，重置 isDraggedOut=false')
      setIsDraggedOut(false) // 重置状态
      return
    }
    
    // 正常停止录音
    console.log('🔧 鼠标抬起：正常停止录音')
    await stopRecording()
    setIsDraggedOut(false) // 重置状态
  }, [stopRecording, isRecording, isDraggedOut])

  // 按住模式：鼠标离开也要结束录音
  const handleMouseLeave = useCallback(async (e: React.MouseEvent) => {
    console.log('🔧 MouseLeave 事件触发', { 
      mode: VOICE_INPUT_MODE, 
      isHolding: isHoldingRef.current, 
      isRecording 
    })
    
    if (VOICE_INPUT_MODE !== 'hold') {
      return
    }
    
    // 在移动端，避免鼠标事件干扰触摸事件
    if ('ontouchstart' in window) {
      console.log('🔧 移动端设备，忽略鼠标离开事件')
      return
    }
    
    if (isHoldingRef.current && isRecording) {
      console.log('🔧 鼠标离开触发取消录音')
      isHoldingRef.current = false
      setIsDraggedOut(true) // 标记为拖拽出区域
      showToast({
        type: 'text',
        content: '已取消发送'
      })
      await cancelRecording() // 立即取消录音
    }
  }, [isRecording, cancelRecording])

  // 按住模式：鼠标重新进入按钮区域
  const handleMouseEnter = useCallback(async (e: React.MouseEvent) => {
    console.log('🔧 MouseEnter 事件触发', { 
      mode: VOICE_INPUT_MODE, 
      isHolding: isHoldingRef.current, 
      isRecording, 
      isDraggedOut 
    })
    
    if (VOICE_INPUT_MODE !== 'hold') {
      return
    }
    
    // 在移动端，避免鼠标事件干扰触摸事件
    if ('ontouchstart' in window) {
      console.log('🔧 移动端设备，忽略鼠标进入事件')
      return
    }
    
    if (isHoldingRef.current && isRecording && isDraggedOut) {
      console.log('🔧 鼠标重新进入，取消拖拽状态')
      setIsDraggedOut(false) // 取消拖拽出区域状态
      // 清除之前的提示，可以重新显示正常录音状态
    }
  }, [isRecording, isDraggedOut])

  // 按住模式：添加触摸事件支持移动端
  const handleTouchStart = useCallback(async (e: React.TouchEvent) => {
    e.stopPropagation()
    console.log('🔧 TouchStart 事件触发', { 
      mode: VOICE_INPUT_MODE, 
      isRecording, 
      error, 
      isHolding: isHoldingRef.current 
    })
    
    if (VOICE_INPUT_MODE !== 'hold') {
      return
    }
    
    if (!isSupported()) {
      onError?.('浏览器不支持语音录制')
      return
    }
    
    if (error || isRecording) {
      console.log('🔧 TouchStart 早期返回', { error, isRecording })
      return
    }
    
    // 记录触摸开始位置
    const touch = e.touches[0]
    const startPosition = {
      x: touch.clientX,
      y: touch.clientY
    }
    touchStartPositionRef.current = startPosition
    console.log('🔧 记录触摸开始位置', startPosition)
    
    // 先检查权限再开始录音
    const hasPermission = await checkAndRequestPermission()
    if (!hasPermission) {
      // 权限被拒绝，取消本次操作
      // 重置触摸状态
      touchStartPositionRef.current = null
      return
    }

    isHoldingRef.current = true
    setIsDraggedOut(false) // 重置拖拽状态
    console.log('🔧 设置 isHolding=true, isDraggedOut=false')
    
    const success = await startRecording()
    console.log('🔧 startRecording 结果', { success })
  }, [startRecording, isSupported, checkAndRequestPermission, error, isRecording])

  const handleTouchEnd = useCallback(async (e: React.TouchEvent) => {
    e.stopPropagation()
    console.log('🔧 TouchEnd 事件触发', { 
      mode: VOICE_INPUT_MODE, 
      isHolding: isHoldingRef.current, 
      isRecording, 
      isDraggedOut 
    })
    
    if (VOICE_INPUT_MODE !== 'hold') {
      return
    }
    
    if (!isHoldingRef.current || !isRecording) {
      console.log('🔧 TouchEnd 早期返回', { isHolding: isHoldingRef.current, isRecording })
      return
    }
    
    isHoldingRef.current = false
    
    // 重置触摸位置记录
    touchStartPositionRef.current = null
    console.log('🔧 重置状态: isHolding=false, touchStartPosition=null')
    
    // 如果已经拖拽出了按钮区域，说明已经取消了，不需要再处理
    if (isDraggedOut) {
      console.log('🔧 已经拖拽出区域，重置 isDraggedOut=false')
      setIsDraggedOut(false) // 重置状态
      return
    }
    
    // 正常停止录音
    console.log('🔧 正常停止录音')
    await stopRecording()
    setIsDraggedOut(false) // 重置状态
  }, [stopRecording, isRecording, isDraggedOut])

  // 触摸移动事件，检测是否移出按钮区域
  const handleTouchMove = useCallback(async (e: React.TouchEvent) => {
    if (VOICE_INPUT_MODE !== 'hold' || !isHoldingRef.current || !isRecording || !touchStartPositionRef.current) {
      return
    }

    const touch = e.touches[0]
    const startPos = touchStartPositionRef.current
    
    // 计算从开始位置的移动距离
    const deltaX = Math.abs(touch.clientX - startPos.x)
    const deltaY = Math.abs(touch.clientY - startPos.y)
    const totalMovement = Math.sqrt(deltaX * deltaX + deltaY * deltaY)
    
    console.log('🔧 TouchMove 检测', { 
      deltaX, 
      deltaY, 
      totalMovement, 
      threshold: TOUCH_MOVE_THRESHOLD,
      currentPos: { x: touch.clientX, y: touch.clientY },
      startPos
    })
    
    // 只有当移动距离超过阈值时才考虑取消
    if (totalMovement < TOUCH_MOVE_THRESHOLD) {
      return
    }
    
    const element = e.currentTarget as HTMLElement
    const rect = element.getBoundingClientRect()
    
    // 检查触摸点是否在按钮区域内
    const isInside = touch.clientX >= rect.left && 
                    touch.clientX <= rect.right && 
                    touch.clientY >= rect.top && 
                    touch.clientY <= rect.bottom

    // 重点检查向上拖拽的情况（取消发送）
    const isUpwardDrag = (touch.clientY - startPos.y) < -TOUCH_MOVE_THRESHOLD
    
    console.log('🔧 TouchMove 判断', { 
      isInside, 
      isUpwardDrag, 
      isDraggedOut,
      buttonRect: rect,
      touchY: touch.clientY,
      startY: startPos.y,
      upwardDistance: startPos.y - touch.clientY
    })
    
    if ((!isInside || isUpwardDrag) && !isDraggedOut) {
      console.log('🔧 触发取消录音', { reason: !isInside ? 'outside' : 'upward-drag' })
      isHoldingRef.current = false
      setIsDraggedOut(true)
      showToast({
        type: 'text',
        content: '已取消发送'
      })
      await cancelRecording() // 立即取消录音
    }
  }, [isRecording, isDraggedOut, cancelRecording, TOUCH_MOVE_THRESHOLD])

  // 获取按钮文字
  const getButtonText = () => {
    if (VOICE_INPUT_MODE === 'hold') {
      if (isRecording) {
        return isDraggedOut ? '松开取消发送' : '录音中...'
      }
      return '按住 说话'
    } else {
      return isRecording ? '录音中...' : '点击 开始说话'
    }
  }

  if (!isSupported()) {
    return (
      <div className="flex flex-col items-center justify-center p-4 text-red-500 space-y-2">
        <span>语音功能暂不可用</span>
        {import.meta.env.DEV && (
          <button
            onClick={() => {
              console.log('🔄 重新运行诊断...')
              // runFullDiagnostic() // Removed debug.ts import, so this line is removed
            }}
            className="text-xs bg-gray-200 px-2 py-1 rounded text-gray-700"
          >
            运行诊断
          </button>
        )}
      </div>
    )
  }

  return (
    <>
      <button
        className={`flex-1 rounded bg-white px-4 py-1.5 text-center text-sm transition-colors select-none ${
          error
            ? 'cursor-not-allowed bg-gray-200 text-gray-400'
            : isRecording
            ? 'bg-[#07C160] '
            : 'bg-white text-black hover:bg-gray-50'
        }`}
        onClick={handleClick}
        onMouseDown={handleMouseDown}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseLeave}
        onMouseEnter={handleMouseEnter}
        onTouchStart={handleTouchStart}
        onTouchEnd={handleTouchEnd}
        onTouchMove={handleTouchMove}
        disabled={error !== null}
        type="button"
      >
        {getButtonText()}
      </button>
      
      {/* 波形组件始终存在，通过可见性控制显示 */}
      <div className={`absolute inset-x-4 bottom-16 flex justify-center transition-opacity duration-300 ${
        isRecording ? 'opacity-100 visible' : 'opacity-0 invisible'
      }`}>
        <VoiceWaveform isRecording={isRecording} audioLevels={audioLevels} />
      </div>
    </>
  )
}

export default VoiceInput 