import { useEffect, useState } from 'react'
import { TaskCenter } from '@/pages/task-center/task-center.tsx'
import { IonPage } from '@ionic/react'
import BetaInfoDialog from '@/components/BetaInfoDialog.tsx'
import ConfirmationDialogManager from '@/components/ConfirmationDialogManager'
import ConversationFooter from './ConversationFooter'
import ConversationHeader from './ConversationHeader'
import ConversationList from './ConversationList'
import { ConversationAPIProvider } from './context'

// 内部组件，用于渲染对话内容
const ConversationContent = () => {
  return (
    <div className='mx-auto flex h-full w-full flex-col overflow-hidden bg-gray-100 sm:max-w-md'>
      <ConversationHeader />
      <ConversationList />
      <ConversationFooter />
    </div>
  )
}

const Conversation = () => {
  const [showBetaInfo, setShowBetaInfo] = useState(false)
  const [dialogKey, setDialogKey] = useState(0)

  useEffect(() => {
    // 页面加载时显示内测说明
    setShowBetaInfo(true)
    setDialogKey((prev) => prev + 1)
  }, [])

  const handleCloseBetaInfo = () => {
    setShowBetaInfo(false)
  }

  return (
    <>
      <TaskCenter className='mx-auto sm:max-w-md' />
      <IonPage id='main-content'>
        <ConversationAPIProvider>
          <ConversationContent />
          <BetaInfoDialog
            key={dialogKey}
            isOpen={showBetaInfo}
            onClose={handleCloseBetaInfo}
          />
          {/* 独立的确认对话框管理器 */}
          <ConfirmationDialogManager />
        </ConversationAPIProvider>
      </IonPage>
    </>
  )
}

export default Conversation
