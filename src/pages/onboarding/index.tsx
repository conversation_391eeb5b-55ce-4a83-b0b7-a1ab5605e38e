import { useState } from 'react'
import {
  onboardingQuestions,
  type QuestionOption,
} from '@/pages/onboarding/questionData.ts'
import { ChatServiceManager } from '@/tina/services/chat-service-manager'
import { IonContent, IonPage } from '@ionic/react'
import { useHistory } from 'react-router-dom'
import OnboardingFooter from './OnboardingFooter'
import OnboardingHeader from './OnboardingHeader'
import QuestionsStage from './QuestionsStage'
import ThinkingStage from './ThinkingStage.tsx'

// 添加 fadeIn 动画样式
const fadeInStyle = `
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }
`

// 引导页面的不同阶段
enum OnboardingStage {
  QUESTIONS = 'questions',
  THINKING = 'thinking_complete',
}

const OnboardingPage = () => {
  const [currentStage, setCurrentStage] = useState<OnboardingStage>(
    OnboardingStage.QUESTIONS,
  )
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [apiResponseForNextQuestion, setApiResponseForNextQuestion] = useState<
    string | null
  >(null)
  // 收集用户选择的选项数据
  const [selectedOptions, setSelectedOptions] = useState<QuestionOption[]>([])
  const history = useHistory()

  const handleEnterChat = () => {
    // 跳转到主聊天页面
    history.push('/conversation/1')
  }

  return (
    <IonPage>
      <style>{fadeInStyle}</style>
      <div className='mx-auto flex h-full w-full flex-col overflow-hidden bg-gray-100 sm:max-w-md'>
        {/* 顶部 AppBar */}
        <OnboardingHeader />

        {/* 主要内容区域 */}
        <IonContent className='bg-gray-100'>
          <div className='flex h-full flex-col space-y-4 px-4 py-4'>
            {currentStage === OnboardingStage.QUESTIONS && (
              <QuestionsStage
                currentQuestionIndex={currentQuestionIndex}
                apiResponseText={apiResponseForNextQuestion}
                onQuestionComplete={async (
                  index: number,
                  apiResponse: string,
                  selectedOption: QuestionOption,
                ) => {
                  // 收集选择的选项
                  const newSelectedOptions = [
                    ...selectedOptions,
                    selectedOption,
                  ]
                  setSelectedOptions(newSelectedOptions)

                  if (index === onboardingQuestions.length - 1) {
                    // 最后一个问题完成时，提交用户档案数据
                    try {
                      // 过滤掉值为 'skip' 的选项
                      const validOptions = newSelectedOptions.filter(
                        (option) =>
                          !option.id.includes('skip') &&
                          option.text !== '稍后再说',
                      )

                      // 将选项文字内容用分号连接
                      const profileContent = validOptions
                        .map((option) => option.text)
                        .join(';')

                      if (profileContent) {
                        console.log('提交用户档案数据:', profileContent)
                        await ChatServiceManager.sendProfileMessage(
                          profileContent,
                        )
                      }
                    } catch (error) {
                      console.error('提交用户档案数据失败:', error)
                      // 这里可以添加用户友好的错误提示，但不阻止进入思考阶段
                    }

                    setCurrentStage(OnboardingStage.THINKING)
                  } else {
                    setApiResponseForNextQuestion(apiResponse)
                    setCurrentQuestionIndex(index + 1)
                  }
                }}
              />
            )}

            {currentStage === OnboardingStage.THINKING && (
              <ThinkingStage onEnterChat={handleEnterChat} />
            )}
          </div>
        </IonContent>

        {/* 底部 Footer */}
        <OnboardingFooter />
      </div>
    </IonPage>
  )
}

export default OnboardingPage
