// 问答数据结构定义

export interface QuestionOption {
  id: string
  text: string
}

export interface Question {
  id: string
  text: string
  options: QuestionOption[]
}

export interface AIResponse {
  summary: string[] // 对用户回答的总结
}

// Welcome 文本
export const welcomeTexts = [
  '♪ 滴—答♪ 初次见面！欢迎来到「私人助理」俱乐部',
  '仅需 1分钟 完成 4道选择题',
  '系统就能自动锁定最适合您的助理类型！',
]

// 静态问题数据
export const onboardingQuestions: Question[] = [
  {
    id: 'age',
    text: '1. 您属于哪个年龄区间呢？',
    options: [
      { id: 'age_10_17', text: '10 - 17 岁' },
      { id: 'age_18_35', text: '18 - 35 岁' },
      { id: 'age_36_55', text: '36 - 55 岁' },
      { id: 'age_55_plus', text: '55 岁以上' },
      { id: 'age_skip', text: '稍后再说' },
    ],
  },
  {
    id: 'lifestyle',
    text: '2. 您最近处于哪种生活节奏呢？',
    options: [
      { id: 'lifestyle_study', text: '📖 求学阶段', },
      { id: 'lifestyle_work', text: '💼 职场冲刺', },
      { id: 'lifestyle_job_hunting', text: '🌟 职场进阶', },
      { id: 'lifestyle_creative', text: '🧑‍💻 灵活创收', },
      { id: 'lifestyle_family', text: '👨‍👩‍👧‍👦 家庭时光', },
      { id: 'lifestyle_leisure', text: '🏖️ 休闲探索',},
      { id: 'lifestyle_skip', text: '稍后再说' },
    ],
  },
  {
    id: 'interests',
    text: '3. 您平时最感兴趣的话题是？',
    options: [
      { id: 'interests_finance', text: '💰 理财/金融' },
      { id: 'interests_tech', text: '🚀 科技/创新' },
      { id: 'interests_health', text: '🏃 健康/运动' },
      { id: 'interests_learn', text: '📚 学习/语言' },
      { id: 'interests_entertainment', text: '🎬 娱乐/影音' },
      { id: 'interests_travel', text: '✈️ 旅行/生活饪' },
      { id: 'interests_skip', text: '稍后再说' },
    ],
  },
  {
    id: 'style',
    text: '4. 您希望助理用什么风格和您交流？',
    options: [
      { id: 'style_professional', text: '🤵专业严谨型' },
      { id: 'style_humorous', text: '😄 幽默风趣型' },
      { id: 'style_friendly', text: '😊 温暖贴心型' },
      { id: 'style_creative', text: '🎨 惊喜混搭型' },
      { id: 'style_skip', text: '稍后再说' },
    ],
  },
]

// 思考过程文本
export const thinkingTexts = [
  '由于用户说明了表达了自己的年龄段、现状感想、兴趣领域、以及沟通风格偏好。看起来 ta 是想要找到最懂 ta 的私人助理。',
  `正在分析您的回答...
  根据您的选择，我发现您是一个很有趣的人,
  让我为您匹配最合适的助理风格,
  正在生成个性化的交流方案...
  几乎完成了，马上就好！`,
]

// 完成引导的欢迎文字
export const completionTexts = [
  '已经匹配到最懂您的私人助手了！',
  // '为了更精准地为您推送本地资讯和服务，我们需要获取您的地理位置',
  // 'Tina 可以读取您的地理位置吗？',
  '最后，有请您的专属私人助手',
]
