import { useState } from 'react'
import ChatBubble, { MessageData } from './ChatBubble'
import { completionTexts, thinkingTexts } from './questionData'

interface ThinkingStageProps {
  onEnterChat: () => void
}

const ThinkingStage = ({ onEnterChat }: ThinkingStageProps) => {
  const [currentTextIndex, setCurrentTextIndex] = useState(0)
  const [showButton, setShowButton] = useState(false)
  const [firstMessageTextVisible, setFirstMessageTextVisible] = useState(false)

  const handleThinkingComplete = (duration: number) => {
    // 思考完成后显示第一条消息的文字内容
    // 延迟一点显示文字，确保思考气泡已经完全折叠
    setTimeout(() => {
      setFirstMessageTextVisible(true)
    }, 300)
  }

  const handleTextComplete = (index: number) => {
    if (index < completionTexts.length - 1) {
      setTimeout(() => setCurrentTextIndex((prev) => prev + 1), 1000)
    } else {
      setTimeout(() => setShowButton(true), 1500)
    }
  }

  // 构建消息数据
  const messages: MessageData[] = completionTexts.map((text, index) => ({
    text,
    think:
      index === 0
        ? {
            segments: thinkingTexts,
            startTime: Date.now(),
          }
        : undefined,
  }))

  return (
    <div className='flex w-full flex-col space-y-4'>
      <div className='min-h-[150px] space-y-4'>
        {messages.map((message, index) => (
          <div key={index}>
            {index <= currentTextIndex && (
              <ChatBubble
                message={message}
                onThinkingComplete={
                  index === 0 ? handleThinkingComplete : undefined
                }
                onTextComplete={
                  index === currentTextIndex
                    ? () => handleTextComplete(index)
                    : undefined
                }
                showText={index === 0 ? firstMessageTextVisible : true}
              />
            )}
          </div>
        ))}
      </div>

      {/* 进入聊天按钮 */}
      {showButton && (
        <div className='flex justify-center'>
          <button
            onClick={onEnterChat}
            className='animate-fade-in mt-8 rounded-full px-8 py-4 text-xl font-bold text-gray-800 shadow-md transition-all duration-300 hover:opacity-80'
            style={{
              animation: 'fadeIn 0.5s ease-in-out forwards',
              backgroundColor: '#F6F4EE',
            }}
          >
            遇见您独属的 Tina →
          </button>
        </div>
      )}
    </div>
  )
}

export default ThinkingStage
