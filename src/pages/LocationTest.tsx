import React, { useState } from 'react'
import {
  IonContent,
  IonHeader,
  IonPage,
  IonTitle,
  IonToolbar,
  IonButton,
  IonItem,
  IonLabel,
  IonList,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
} from '@ionic/react'
import NativeLocation from '../plugins/NativeLocation'
import {
  checkLocationPermissions,
  requestLocationPermissions,
  getCurrentLocation,
  getCurrentLocationNative,
  getLocationWithPermission,
  formatLocationInfo,
  LocationInfo,
  LocationError,
} from '../utils/locationService'

const LocationTest: React.FC = () => {
  const [permissionStatus, setPermissionStatus] = useState<string>('')
  const [locationInfo, setLocationInfo] = useState<LocationInfo | null>(null)
  const [error, setError] = useState<LocationError | null>(null)
  const [loading, setLoading] = useState<boolean>(false)

  const handleCheckPermissions = async () => {
    setLoading(true)
    setError(null)

    try {
      const result = await checkLocationPermissions()
      setPermissionStatus(
        `权限状态: ${result.status} (${result.granted ? '已授予' : '未授予'})`,
      )
    } catch (err: any) {
      setError({ code: 'CHECK_ERROR', message: err.message })
    } finally {
      setLoading(false)
    }
  }

  const handleRequestPermissions = async () => {
    setLoading(true)
    setError(null)

    try {
      const result = await requestLocationPermissions()
      setPermissionStatus(
        `权限请求结果: ${result.status} (${result.granted ? '已授予' : '未授予'})`,
      )
    } catch (err: any) {
      setError({ code: 'REQUEST_ERROR', message: err.message })
    } finally {
      setLoading(false)
    }
  }

  const handleGetLocation = async () => {
    setLoading(true)
    setError(null)
    setLocationInfo(null)

    try {
      const result = await getCurrentLocation()
      if (result.success && result.location) {
        setLocationInfo(result.location)
        console.log('位置信息:', formatLocationInfo(result.location))
      } else {
        setError(result.error || { code: 'UNKNOWN', message: '获取位置失败' })
      }
    } catch (err: any) {
      setError({ code: 'LOCATION_ERROR', message: err.message })
    } finally {
      setLoading(false)
    }
  }

  const handleGetNativeLocation = async () => {
    setLoading(true)
    setError(null)
    setLocationInfo(null)

    try {
      const result = await getCurrentLocationNative()
      if (result.success && result.location) {
        setLocationInfo(result.location)
        console.log('原生定位信息:', formatLocationInfo(result.location))
      } else {
        setError(result.error || { code: 'UNKNOWN', message: '原生定位失败' })
      }
    } catch (err: any) {
      setError({ code: 'NATIVE_LOCATION_ERROR', message: err.message })
    } finally {
      setLoading(false)
    }
  }

  const handleFullLocationFlow = async () => {
    setLoading(true)
    setError(null)
    setLocationInfo(null)
    setPermissionStatus('')

    try {
      const result = await getLocationWithPermission()

      if (result.permissionStatus) {
        setPermissionStatus(`权限状态: ${result.permissionStatus}`)
      }

      if (result.success && result.location) {
        setLocationInfo(result.location)
        console.log('完整流程成功:', formatLocationInfo(result.location))
      } else {
        setError(result.error || { code: 'UNKNOWN', message: '定位流程失败' })
      }
    } catch (err: any) {
      setError({ code: 'FLOW_ERROR', message: err.message })
    } finally {
      setLoading(false)
    }
  }

  const handleTestPlugin = async () => {
    setLoading(true)
    setError(null)
    setPermissionStatus('')

    try {
      const result = await NativeLocation.testPlugin()
      setPermissionStatus(
        `插件测试: ${result.message} (时间戳: ${result.timestamp})`,
      )
      console.log('插件测试成功:', result)
    } catch (err: any) {
      setError({ code: 'PLUGIN_TEST_ERROR', message: err.message })
      console.error('插件测试失败:', err)
    } finally {
      setLoading(false)
    }
  }

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar>
          <IonTitle>定位功能测试</IonTitle>
        </IonToolbar>
      </IonHeader>
      <IonContent fullscreen>
        <div className='p-4'>
          <IonCard>
            <IonCardHeader>
              <IonCardTitle>定位功能测试</IonCardTitle>
            </IonCardHeader>
            <IonCardContent>
              <IonList>
                <IonItem>
                  <IonButton
                    expand='block'
                    color='tertiary'
                    onClick={handleTestPlugin}
                    disabled={loading}
                  >
                    🔧 测试插件连接
                  </IonButton>
                </IonItem>

                <IonItem>
                  <IonButton
                    expand='block'
                    onClick={handleCheckPermissions}
                    disabled={loading}
                  >
                    检查权限状态
                  </IonButton>
                </IonItem>

                <IonItem>
                  <IonButton
                    expand='block'
                    onClick={handleRequestPermissions}
                    disabled={loading}
                  >
                    请求定位权限
                  </IonButton>
                </IonItem>

                <IonItem>
                  <IonButton
                    expand='block'
                    onClick={handleGetLocation}
                    disabled={loading}
                  >
                    获取当前位置 (Capacitor)
                  </IonButton>
                </IonItem>

                <IonItem>
                  <IonButton
                    expand='block'
                    color='secondary'
                    onClick={handleGetNativeLocation}
                    disabled={loading}
                  >
                    获取当前位置 (Android 原生)
                  </IonButton>
                </IonItem>

                <IonItem>
                  <IonButton
                    expand='block'
                    color='primary'
                    onClick={handleFullLocationFlow}
                    disabled={loading}
                  >
                    完整定位流程
                  </IonButton>
                </IonItem>
              </IonList>
            </IonCardContent>
          </IonCard>

          {loading && (
            <IonCard>
              <IonCardContent>
                <IonLabel>处理中...</IonLabel>
              </IonCardContent>
            </IonCard>
          )}

          {permissionStatus && (
            <IonCard>
              <IonCardHeader>
                <IonCardTitle>权限状态</IonCardTitle>
              </IonCardHeader>
              <IonCardContent>
                <IonLabel>{permissionStatus}</IonLabel>
              </IonCardContent>
            </IonCard>
          )}

          {locationInfo && (
            <IonCard>
              <IonCardHeader>
                <IonCardTitle>位置信息</IonCardTitle>
              </IonCardHeader>
              <IonCardContent>
                <pre style={{ whiteSpace: 'pre-wrap', fontSize: '14px' }}>
                  {formatLocationInfo(locationInfo)}
                </pre>
              </IonCardContent>
            </IonCard>
          )}

          {error && (
            <IonCard color='danger'>
              <IonCardHeader>
                <IonCardTitle>错误信息</IonCardTitle>
              </IonCardHeader>
              <IonCardContent>
                <IonLabel>
                  <strong>错误代码:</strong> {error.code}
                  <br />
                  <strong>错误信息:</strong> {error.message}
                </IonLabel>
              </IonCardContent>
            </IonCard>
          )}
        </div>
      </IonContent>
    </IonPage>
  )
}

export default LocationTest
