import { Geolocation } from '@capacitor/geolocation'
import { Capacitor } from '@capacitor/core'

export interface LocationInfo {
  latitude: number
  longitude: number
  accuracy: number
  timestamp: number
}

export interface LocationError {
  code: string
  message: string
}

/**
 * 检查定位权限状态
 */
export const checkLocationPermissions = async (): Promise<{
  granted: boolean
  status: string
}> => {
  try {
    const permissions = await Geolocation.checkPermissions()
    const isGranted = permissions.location === 'granted'
    
    return {
      granted: isGranted,
      status: permissions.location
    }
  } catch (error) {
    console.error('检查定位权限失败:', error)
    return {
      granted: false,
      status: 'error'
    }
  }
}

/**
 * 请求定位权限
 */
export const requestLocationPermissions = async (): Promise<{
  granted: boolean
  status: string
}> => {
  try {
    // 在 Web 环境下，直接返回 granted 状态
    if (Capacitor.getPlatform() === 'web') {
      return {
        granted: true,
        status: 'granted'
      }
    }

    const permissions = await Geolocation.requestPermissions()
    const isGranted = permissions.location === 'granted'
    
    return {
      granted: isGranted,
      status: permissions.location
    }
  } catch (error) {
    console.error('请求定位权限失败:', error)
    return {
      granted: false,
      status: 'denied'
    }
  }
}

/**
 * 获取当前位置
 */
export const getCurrentLocation = async (): Promise<{
  success: boolean
  location?: LocationInfo
  error?: LocationError
}> => {
  try {
    const position = await Geolocation.getCurrentPosition({
      enableHighAccuracy: true,
      timeout: 10000,
      maximumAge: 60000
    })

    const locationInfo: LocationInfo = {
      latitude: position.coords.latitude,
      longitude: position.coords.longitude,
      accuracy: position.coords.accuracy,
      timestamp: position.timestamp
    }

    return {
      success: true,
      location: locationInfo
    }
  } catch (error: any) {
    console.error('获取位置失败:', error)
    
    const locationError: LocationError = {
      code: error.code || 'UNKNOWN_ERROR',
      message: error.message || '获取位置失败'
    }

    return {
      success: false,
      error: locationError
    }
  }
}

/**
 * 完整的定位流程：检查权限 -> 请求权限 -> 获取位置
 */
export const getLocationWithPermission = async (): Promise<{
  success: boolean
  location?: LocationInfo
  error?: LocationError
  permissionStatus?: string
}> => {
  try {
    console.log('开始定位流程...')
    
    // 1. 检查当前权限状态
    const permissionCheck = await checkLocationPermissions()
    console.log('权限检查结果:', permissionCheck)
    
    let permissionGranted = permissionCheck.granted
    let permissionStatus = permissionCheck.status
    
    // 2. 如果权限未授予，尝试请求权限
    if (!permissionGranted) {
      console.log('权限未授予，正在请求权限...')
      const permissionRequest = await requestLocationPermissions()
      console.log('权限请求结果:', permissionRequest)
      
      permissionGranted = permissionRequest.granted
      permissionStatus = permissionRequest.status
    }
    
    // 3. 如果权限仍未授予，返回错误
    if (!permissionGranted) {
      return {
        success: false,
        error: {
          code: 'PERMISSION_DENIED',
          message: '用户拒绝了定位权限申请'
        },
        permissionStatus
      }
    }
    
    // 4. 获取位置信息
    console.log('权限已授予，正在获取位置...')
    const locationResult = await getCurrentLocation()
    
    if (locationResult.success && locationResult.location) {
      console.log('定位成功:', locationResult.location)
      return {
        success: true,
        location: locationResult.location,
        permissionStatus
      }
    } else {
      return {
        success: false,
        error: locationResult.error,
        permissionStatus
      }
    }
  } catch (error: any) {
    console.error('定位流程异常:', error)
    return {
      success: false,
      error: {
        code: 'LOCATION_FLOW_ERROR',
        message: error.message || '定位流程发生异常'
      }
    }
  }
}

/**
 * 格式化位置信息用于显示
 */
export const formatLocationInfo = (location: LocationInfo): string => {
  return `位置信息:
纬度: ${location.latitude.toFixed(6)}
经度: ${location.longitude.toFixed(6)}
精度: ${location.accuracy.toFixed(0)}米
时间: ${new Date(location.timestamp).toLocaleString()}`
}
