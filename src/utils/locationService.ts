import { Capacitor } from '@capacitor/core'
import { Geolocation } from '@capacitor/geolocation'

export interface LocationInfo {
  latitude: number
  longitude: number
  accuracy: number
  timestamp: number
}

export interface LocationError {
  code: string
  message: string
}

/**
 * 检查定位权限状态
 */
export const checkLocationPermissions = async (): Promise<{
  granted: boolean
  status: string
}> => {
  try {
    const permissions = await Geolocation.checkPermissions()
    const isGranted = permissions.location === 'granted'

    return {
      granted: isGranted,
      status: permissions.location,
    }
  } catch (error) {
    console.error('检查定位权限失败:', error)
    return {
      granted: false,
      status: 'error',
    }
  }
}

/**
 * 请求定位权限
 */
export const requestLocationPermissions = async (): Promise<{
  granted: boolean
  status: string
}> => {
  try {
    // 在 Web 环境下，直接返回 granted 状态
    if (Capacitor.getPlatform() === 'web') {
      return {
        granted: true,
        status: 'granted',
      }
    }

    const permissions = await Geolocation.requestPermissions()
    const isGranted = permissions.location === 'granted'

    return {
      granted: isGranted,
      status: permissions.location,
    }
  } catch (error) {
    console.error('请求定位权限失败:', error)
    return {
      granted: false,
      status: 'denied',
    }
  }
}

/**
 * 获取当前位置
 */
export const getCurrentLocation = async (): Promise<{
  success: boolean
  location?: LocationInfo
  error?: LocationError
}> => {
  try {
    const position = await Geolocation.getCurrentPosition({
      enableHighAccuracy: true,
      timeout: 10000,
      maximumAge: 60000,
    })

    const locationInfo: LocationInfo = {
      latitude: position.coords.latitude,
      longitude: position.coords.longitude,
      accuracy: position.coords.accuracy,
      timestamp: position.timestamp,
    }

    return {
      success: true,
      location: locationInfo,
    }
  } catch (error: any) {
    console.error('获取位置失败:', error)

    const locationError: LocationError = {
      code: error.code || 'UNKNOWN_ERROR',
      message: error.message || '获取位置失败',
    }

    return {
      success: false,
      error: locationError,
    }
  }
}

/**
 * 使用 navigator 获取当前位置
 */
export const getCurrentLocationNavigator = async (): Promise<{
  success: boolean
  location?: LocationInfo
  error?: LocationError
}> => {
  return new Promise((resolve) => {
    if (!navigator.geolocation) {
      const error: LocationError = {
        code: 'GEOLOCATION_NOT_SUPPORTED',
        message: '当前浏览器不支持 geolocation API',
      }
      console.error('获取位置失败:', error)
      return resolve({ success: false, error })
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const locationInfo: LocationInfo = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy,
          timestamp: position.timestamp,
        }
        resolve({ success: true, location: locationInfo })
      },
      (error) => {
        const locationError: LocationError = {
          code: error.code.toString(),
          message: error.message,
        }
        console.error('获取位置失败:', locationError)
        resolve({ success: false, error: locationError })
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 60000,
      },
    )
  })
}

/**
 * 完整的定位流程：检查权限 -> 请求权限 -> 获取位置
 */
export const getLocationWithPermission = async (): Promise<{
  success: boolean
  location?: LocationInfo
  error?: LocationError
  permissionStatus?: string
}> => {
  try {
    console.log('开始定位流程...')

    // 1. 检查当前权限状态
    const permissionCheck = await checkLocationPermissions()
    console.log('权限检查结果:', permissionCheck)

    let permissionGranted = permissionCheck.granted
    let permissionStatus = permissionCheck.status

    // 2. 如果权限未授予，尝试请求权限
    if (!permissionGranted) {
      console.log('权限未授予，正在请求权限...')
      const permissionRequest = await requestLocationPermissions()
      console.log('权限请求结果:', permissionRequest)

      permissionGranted = permissionRequest.granted
      permissionStatus = permissionRequest.status
    }

    // 3. 如果权限仍未授予，返回错误
    if (!permissionGranted) {
      return {
        success: false,
        error: {
          code: 'PERMISSION_DENIED',
          message: '用户拒绝了定位权限申请',
        },
        permissionStatus,
      }
    }

    // 4. 获取位置信息
    console.log('权限已授予，正在获取位置...')
    const locationResult = await getCurrentLocationNavigator()

    if (locationResult.success && locationResult.location) {
      console.log('定位成功:', locationResult.location)
      return {
        success: true,
        location: locationResult.location,
        permissionStatus,
      }
    } else {
      return {
        success: false,
        error: locationResult.error,
        permissionStatus,
      }
    }
  } catch (error: any) {
    console.error('定位流程异常:', error)
    return {
      success: false,
      error: {
        code: 'LOCATION_FLOW_ERROR',
        message: error.message || '定位流程发生异常',
      },
    }
  }
}

/**
 * 通过 IP 地址获取大概位置（备用方案）
 */
export const getLocationByIP = async (): Promise<{
  success: boolean
  location?: LocationInfo
  error?: LocationError
}> => {
  try {
    console.log('尝试通过 IP 地址获取位置...')

    // 使用免费的 IP 地理位置服务
    const response = await fetch('https://ipapi.co/json/')

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()

    if (data.latitude && data.longitude) {
      const locationInfo: LocationInfo = {
        latitude: parseFloat(data.latitude),
        longitude: parseFloat(data.longitude),
        accuracy: 10000, // IP 定位精度较低，设置为 10km
        timestamp: Date.now(),
      }

      console.log('IP 定位成功:', locationInfo)
      return {
        success: true,
        location: locationInfo,
      }
    } else {
      throw new Error('IP 定位服务返回数据无效')
    }
  } catch (error: any) {
    console.error('IP 定位失败:', error)
    return {
      success: false,
      error: {
        code: 'IP_LOCATION_ERROR',
        message: error.message || 'IP 定位失败',
      },
    }
  }
}

/**
 * 多重定位策略：优先使用 GPS，失败后使用 IP 定位
 */
export const getLocationMultiStrategy = async (): Promise<{
  success: boolean
  location?: LocationInfo
  error?: LocationError
  method?: string
}> => {
  try {
    console.log('开始多重定位策略...')

    // 策略1: 尝试原生 GPS 定位
    console.log('尝试 GPS 定位...')
    const gpsResult = await getCurrentLocationNavigator()

    if (gpsResult.success && gpsResult.location) {
      console.log('GPS 定位成功')
      return {
        success: true,
        location: gpsResult.location,
        method: 'GPS',
      }
    }

    console.log('GPS 定位失败，尝试 IP 定位...')

    // 策略2: GPS 失败后使用 IP 定位
    const ipResult = await getLocationByIP()

    if (ipResult.success && ipResult.location) {
      console.log('IP 定位成功')
      return {
        success: true,
        location: ipResult.location,
        method: 'IP',
      }
    }

    // 所有策略都失败
    return {
      success: false,
      error: {
        code: 'ALL_LOCATION_METHODS_FAILED',
        message: 'GPS 和 IP 定位都失败了',
      },
    }
  } catch (error: any) {
    console.error('多重定位策略异常:', error)
    return {
      success: false,
      error: {
        code: 'MULTI_STRATEGY_ERROR',
        message: error.message || '多重定位策略发生异常',
      },
    }
  }
}

/**
 * 格式化位置信息用于显示
 */
export const formatLocationInfo = (location: LocationInfo): string => {
  return `位置信息:
纬度: ${location.latitude.toFixed(6)}
经度: ${location.longitude.toFixed(6)}
精度: ${location.accuracy.toFixed(0)}米
时间: ${new Date(location.timestamp).toLocaleString()}`
}
